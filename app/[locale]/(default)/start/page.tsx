import Link from "next/link";

export default function StartPage() {
  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* 左侧边栏 */}
      <aside className="w-64 bg-white border-r flex flex-col py-8 px-4">
        <h2 className="text-xl font-bold mb-8">导航</h2>
        <nav className="flex flex-col gap-4">
          <Link href="/dashboard" className="text-gray-700 hover:text-blue-600 font-medium">仪表盘</Link>
          <Link href="/settings" className="text-gray-700 hover:text-blue-600 font-medium">设置</Link>
        </nav>
      </aside>
      {/* 主内容区 */}
      <main className="flex-1 flex flex-col items-center justify-center">
        <h1 className="text-3xl font-bold mb-4">欢迎来到开始页</h1>
        <p className="text-gray-600">请选择左侧功能，开启你的专利助手体验。</p>
      </main>
    </div>
  );
} 